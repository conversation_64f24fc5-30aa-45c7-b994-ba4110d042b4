#!/usr/bin/env python3
"""
测试 ni64x64.png 压缩功能
"""

import os
import subprocess
import sys

def create_test_image():
    """创建一个测试用的64x64图片"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建64x64的测试图片
        img = Image.new('RGB', (64, 64), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制一些简单的图形
        draw.rectangle([5, 5, 59, 59], fill='lightblue', outline='darkblue', width=2)
        draw.ellipse([15, 15, 49, 49], fill='yellow', outline='orange', width=2)
        
        # 绘制文字
        try:
            # 尝试使用默认字体
            draw.text((22, 28), "NI", fill='black')
        except:
            # 如果字体加载失败，绘制简单图形代替
            draw.rectangle([25, 25, 35, 35], fill='red')
            draw.rectangle([30, 30, 40, 40], fill='green')
        
        img.save("ni64x64.png")
        print("✓ 创建了测试图片 ni64x64.png (64x64像素)")
        return True
        
    except ImportError:
        print("警告: 需要安装PIL库来创建测试图片")
        print("运行: pip install Pillow")
        
        # 尝试创建一个简单的PNG文件（如果有其他方法）
        return create_simple_png()
        
    except Exception as e:
        print(f"创建测试图片失败: {str(e)}")
        return False

def create_simple_png():
    """创建一个简单的PNG文件（不依赖PIL）"""
    try:
        # 使用pypng库创建简单图片
        import png
        
        # 创建64x64的简单图案
        width, height = 64, 64
        img_data = []
        
        for y in range(height):
            row = []
            for x in range(width):
                # 创建简单的棋盘图案
                if (x // 8 + y // 8) % 2 == 0:
                    row.extend([255, 200, 100])  # 橙色
                else:
                    row.extend([100, 150, 255])  # 蓝色
            img_data.append(row)
        
        # 保存PNG文件
        with open('ni64x64.png', 'wb') as f:
            w = png.Writer(width, height, greyscale=False)
            w.write(f, img_data)
        
        print("✓ 创建了简单测试图片 ni64x64.png (64x64像素)")
        return True
        
    except ImportError:
        print("错误: 需要pypng库，运行: pip install pypng")
        return False
    except Exception as e:
        print(f"创建简单PNG失败: {str(e)}")
        return False

def test_compression():
    """测试图片压缩功能"""
    print("测试LVGL图片压缩功能")
    print("=" * 40)
    
    # 检查是否存在图片文件
    if not os.path.exists("ni64x64.png"):
        print("未找到 ni64x64.png 文件，尝试创建测试图片...")
        if not create_test_image():
            print("无法创建测试图片，请手动放置 ni64x64.png 文件")
            return False
    
    # 显示原始图片信息
    if os.path.exists("ni64x64.png"):
        size = os.path.getsize("ni64x64.png")
        print(f"原始PNG文件大小: {size} 字节")
    
    # 调用LVGLImage.py的压缩功能
    try:
        print("\n开始压缩处理...")
        result = subprocess.run([
            sys.executable, "LVGLImage.py", "--compress-ni64x64"
        ], capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            print("✓ 压缩成功!")
            print("标准输出:")
            print(result.stdout)
            
            # 检查生成的文件
            output_file = "./output/ni64x64.c"
            if os.path.exists(output_file):
                size = os.path.getsize(output_file)
                print(f"\n生成的C文件: {output_file}")
                print(f"C文件大小: {size} 字节")
                
                # 显示C文件的部分内容
                print("\nC文件内容预览:")
                print("-" * 50)
                with open(output_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for i, line in enumerate(lines[:10], 1):
                        print(f"{i:2d}: {line.rstrip()}")
                    if len(lines) > 10:
                        print("    ... (更多内容)")
                print("-" * 50)
                
                return True
            else:
                print("❌ 未找到生成的C文件")
                return False
        else:
            print("❌ 压缩失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("LVGL ni64x64.png 压缩测试工具")
    print("=" * 50)
    
    # 检查依赖
    try:
        import png
        print("✓ pypng 库已安装")
    except ImportError:
        print("❌ 缺少 pypng 库，请运行: pip install pypng")
        return
    
    try:
        import lz4
        print("✓ lz4 库已安装")
    except ImportError:
        print("❌ 缺少 lz4 库，请运行: pip install lz4")
        return
    
    print()
    
    # 执行测试
    success = test_compression()
    
    if success:
        print("\n🎉 测试完成!")
        print("生成的C文件可以在LVGL项目中使用")
        print("\n使用方法:")
        print("1. 将生成的 ni64x64.c 文件添加到您的项目中")
        print("2. 在代码中声明: extern const lv_image_dsc_t ni64x64;")
        print("3. 使用图片: lv_img_set_src(img_obj, &ni64x64);")
    else:
        print("\n❌ 测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
