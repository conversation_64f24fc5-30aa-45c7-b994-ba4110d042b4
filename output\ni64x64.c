
#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
#include "lvgl.h"
#elif defined(LV_LVGL_H_INCLUDE_SYSTEM)
#include <lvgl.h>
#elif defined(LV_BUILD_TEST)
#include "../lvgl.h"
#else
#include "lvgl/lvgl.h"
#endif

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_NI64X64
#define LV_ATTRIBUTE_NI64X64
#endif

static const
LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_NI64X64
uint8_t ni64x64_map[] = {

    0x02,0x00,0x00,0x00,0x84,0x02,0x00,0x00,0x00,0x20,0x00,0x00,0x1f,0xff,0x01,0x00,
    0xff,0xff,0x78,0x2f,0x11,0x00,0x02,0x00,0x59,0x0e,0xf7,0x02,0x0f,0x7e,0x00,0x59,
    0x0f,0x80,0x00,0x05,0x2f,0xbc,0xae,0x02,0x00,0x51,0x0f,0x80,0x00,0xff,0xff,0xff,
    0xb4,0x2c,0x20,0xfd,0x02,0x00,0x0f,0x3a,0x04,0x17,0x0f,0x00,0x04,0x2d,0x0c,0x7a,
    0x00,0x06,0x02,0x00,0x0f,0x84,0x00,0x13,0x0f,0x80,0x00,0x29,0x06,0x6c,0x00,0x28,
    0xe0,0xff,0x02,0x00,0x06,0x18,0x00,0x0f,0x84,0x00,0x0f,0x0f,0x80,0x00,0x25,0x04,
    0x64,0x00,0x08,0x78,0x00,0x0a,0x02,0x00,0x04,0x22,0x00,0x0f,0x84,0x00,0x0b,0x0f,
    0x80,0x00,0x23,0x02,0x5c,0x00,0x0a,0x70,0x00,0x0f,0x02,0x00,0x01,0x02,0x28,0x00,
    0x0f,0x82,0x00,0x09,0x0f,0x80,0x00,0x21,0x02,0x56,0x00,0x0f,0x70,0x00,0x01,0x0e,
    0x02,0x00,0x02,0x2c,0x00,0x0f,0x82,0x00,0x07,0x0f,0x80,0x00,0x1f,0x0f,0x7c,0x00,
    0x17,0x04,0x02,0x00,0x0f,0x82,0x00,0x09,0x0f,0x80,0x00,0x1d,0x02,0xd0,0x00,0x04,
    0x5a,0x00,0x0f,0x02,0x00,0x13,0x02,0x34,0x00,0x0f,0x04,0x01,0x03,0x0f,0x80,0x00,
    0x21,0x0f,0x76,0x00,0x13,0x08,0x02,0x00,0x0f,0x02,0x01,0x07,0x0f,0x80,0x00,0x1b,
    0x0f,0x7c,0x01,0x1f,0x04,0x02,0x00,0x0f,0x82,0x00,0x05,0x0f,0x80,0x00,0x99,0x0f,
    0xfe,0x00,0x27,0x0f,0x02,0x01,0x07,0x0f,0x00,0x01,0x19,0x0f,0x80,0x00,0x6b,0x02,
    0xc6,0x02,0x02,0xcc,0x00,0x0f,0x02,0x00,0x21,0x02,0x40,0x00,0x0c,0x06,0x03,0x0f,
    0x00,0x01,0x17,0x0f,0xfe,0x00,0x2b,0x0f,0x82,0x01,0x05,0x0f,0x80,0x00,0x27,0x42,
    0x20,0x00,0xe0,0x39,0x58,0x00,0x26,0x60,0x08,0x06,0x00,0x0f,0x02,0x00,0x09,0x0f,
    0x04,0x03,0x01,0x0f,0x80,0x00,0x27,0x66,0x60,0x08,0xc0,0x39,0xe0,0xbd,0x7a,0x00,
    0x02,0x86,0x00,0x0f,0x02,0x00,0x09,0x0f,0x80,0x00,0x3d,0x22,0xe0,0xbd,0x02,0x01,
    0x02,0x7a,0x00,0x02,0x06,0x00,0x0f,0x02,0x00,0x09,0x0f,0x80,0x00,0x3e,0x21,0xff,
    0xe0,0x02,0x01,0x02,0x7a,0x00,0x02,0x06,0x00,0x0f,0x02,0x00,0x09,0x0f,0x80,0x00,
    0x40,0x01,0x02,0x01,0x02,0x7a,0x00,0x02,0x06,0x00,0x0f,0x02,0x00,0x09,0x0f,0x80,
    0x00,0x40,0x01,0x02,0x01,0x02,0x7a,0x00,0x02,0x06,0x00,0x0f,0x02,0x00,0x09,0x0f,
    0x80,0x00,0x2f,0x08,0x00,0x04,0x02,0x70,0x00,0x42,0x00,0xc6,0xc0,0x39,0x0a,0x00,
    0x02,0x06,0x00,0x0f,0x02,0x00,0x07,0x0f,0x00,0x04,0x2d,0x0c,0x02,0x04,0x04,0x70,
    0x00,0x46,0x00,0x42,0x20,0x00,0x06,0x03,0x0f,0x02,0x00,0x07,0x0f,0xfe,0x00,0x01,
    0x0f,0x80,0x03,0x19,0x0c,0x80,0x00,0x0f,0x02,0x00,0x1d,0x0f,0x80,0x00,0x2f,0x0f,
    0x82,0x00,0x29,0x0f,0x7e,0x00,0x03,0x0f,0x00,0x01,0x1b,0x0f,0x80,0x00,0x6f,0x0f,
    0x82,0x00,0x25,0x0f,0xfe,0x00,0x05,0x0f,0x00,0x01,0x1d,0x02,0x80,0x00,0x08,0x06,
    0x03,0x0f,0x02,0x00,0x11,0x0f,0xfa,0x02,0x03,0x02,0x02,0x00,0x0f,0x00,0x06,0x17,
    0x04,0x02,0x00,0x0f,0x80,0x00,0x23,0x04,0x3e,0x00,0x0c,0x02,0x00,0x0f,0x80,0x00,
    0x1f,0x02,0x82,0x00,0x0f,0x04,0x01,0x15,0x0f,0xfc,0x00,0x09,0x0f,0x80,0x01,0x21,
    0x02,0x02,0x00,0x02,0x56,0x00,0x0f,0x7c,0x01,0x0f,0x02,0x28,0x00,0x02,0x34,0x00,
    0x0f,0x02,0x00,0x03,0x0f,0x00,0x01,0x21,0x06,0x04,0x01,0x0f,0x84,0x00,0x09,0x02,
    0x7c,0x00,0x0f,0x7e,0x00,0x0b,0x0f,0x80,0x00,0x27,0x08,0x84,0x00,0x0c,0x86,0x00,
    0x04,0x7a,0x00,0x0f,0x7c,0x00,0x0d,0x0f,0x80,0x00,0x2d,0x0a,0x84,0x00,0x0c,0x02,
    0x00,0x02,0x20,0x00,0x0f,0x02,0x00,0x0d,0x0f,0x80,0x01,0x25,0x08,0x02,0x00,0x0c,
    0x7a,0x00,0x0f,0x7c,0x00,0x15,0x0f,0x00,0x01,0x31,0x0f,0x02,0x00,0x2d,0x0f,0x00,
    0x01,0x31,0x0f,0x02,0x00,0x29,0x0f,0x80,0x00,0xff,0xff,0xff,0x0a,0x0f,0x02,0x00,
    0x57,0x0f,0x80,0x1a,0x7f,0x0f,0x02,0x00,0xff,0xdf,0x50,0xff,0xff,0xff,0xff,0xff,

};

const lv_image_dsc_t ni64x64 = {
  .header = {
    .magic = LV_IMAGE_HEADER_MAGIC,
    .cf = LV_COLOR_FORMAT_RGB565,
    .flags = 0 | LV_IMAGE_FLAGS_COMPRESSED,
    .w = 64,
    .h = 64,
    .stride = 128,
    .reserved_2 = 0,
  },
  .data_size = sizeof(ni64x64_map),
  .data = ni64x64_map,
  .reserved = NULL,
};

