#!/usr/bin/env python3
"""
使用 LVGLImage.py 压缩图片并生成C文件的示例脚本
"""

import os
import sys
from LVGLImage import LVGLImage, ColorFormat, CompressMethod, OutputFormat, PNGConverter

def convert_single_image(image_path, output_dir="./output", compress_method="LZ4"):
    """
    转换单个图片文件
    
    Args:
        image_path: 图片文件路径
        output_dir: 输出目录
        compress_method: 压缩方法 ("NONE", "RLE", "LZ4")
    """
    if not os.path.exists(image_path):
        print(f"错误: 图片文件 {image_path} 不存在")
        return False
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    try:
        # 设置压缩方法
        compress = CompressMethod[compress_method]
        
        # 创建转换器
        converter = PNGConverter(
            files=[image_path],
            cf=ColorFormat.RGB565,  # 使用RGB565格式，适合嵌入式系统
            ofmt=OutputFormat.C_ARRAY,  # 输出C数组格式
            odir=output_dir,
            background=0x000000,  # 黑色背景
            align=1,  # 字节对齐
            premultiply=False,  # 不预乘
            compress=compress,  # 压缩方法
            keep_folder=False,  # 不保持文件夹结构
            rgb565_dither=False,  # 不使用抖动
            nema_gfx=False  # 不使用NEMA GFX格式
        )
        
        # 执行转换
        output = converter.convert(outputname=None)
        
        print(f"转换成功!")
        print(f"输入文件: {image_path}")
        print(f"输出目录: {output_dir}")
        print(f"压缩方法: {compress_method}")
        
        for f, img in output:
            print(f"生成的C文件数据大小: {img.data_len} 字节")
            
        return True
        
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return False

def convert_ni64x64():
    """
    专门转换 ni64x64.png 文件的函数
    """
    image_path = "ni64x64.png"
    
    if not os.path.exists(image_path):
        print(f"警告: {image_path} 文件不存在")
        print("请确保 ni64x64.png 文件在当前目录下")
        return False
    
    print("开始转换 ni64x64.png...")
    
    # 使用LZ4压缩
    success = convert_single_image(
        image_path=image_path,
        output_dir="./output",
        compress_method="LZ4"
    )
    
    if success:
        print("\n生成的文件:")
        output_c_file = "./output/ni64x64.c"
        if os.path.exists(output_c_file):
            print(f"- C文件: {output_c_file}")
            
            # 显示生成的C文件的前几行
            with open(output_c_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()[:20]  # 读取前20行
                print("\nC文件内容预览:")
                print("=" * 50)
                for i, line in enumerate(lines, 1):
                    print(f"{i:2d}: {line.rstrip()}")
                if len(lines) == 20:
                    print("... (更多内容)")
                print("=" * 50)
    
    return success

def show_usage():
    """显示使用说明"""
    print("LVGL图片转换工具使用说明:")
    print("=" * 50)
    print("1. 将要转换的PNG图片放在当前目录下")
    print("2. 运行此脚本进行转换")
    print("3. 转换后的C文件将保存在 ./output/ 目录下")
    print("")
    print("支持的压缩方法:")
    print("- NONE: 不压缩")
    print("- RLE: RLE压缩")
    print("- LZ4: LZ4压缩 (推荐)")
    print("")
    print("支持的颜色格式:")
    print("- RGB565: 16位RGB (推荐用于嵌入式)")
    print("- ARGB8888: 32位ARGB")
    print("- RGB888: 24位RGB")
    print("- I8: 8位索引色")
    print("等等...")

if __name__ == "__main__":
    print("LVGL图片转换工具")
    print("=" * 30)
    
    # 显示使用说明
    show_usage()
    print("")
    
    # 尝试转换 ni64x64.png
    if not convert_ni64x64():
        print("\n由于 ni64x64.png 不存在，这里演示如何使用:")
        print("1. 将您的 ni64x64.png 文件放在当前目录")
        print("2. 重新运行此脚本")
        print("3. 或者修改 image_path 变量指向您的图片文件")
