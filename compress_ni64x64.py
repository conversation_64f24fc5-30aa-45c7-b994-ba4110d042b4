#!/usr/bin/env python3
"""
专门用于压缩 ni64x64.png 并生成C文件的脚本
基于 LVGLImage.py 修改
"""

import os
import sys
from LVGLImage import LVGLImage, ColorFormat, CompressMethod

def compress_ni64x64_image():
    """
    压缩 ni64x64.png 图片并生成C文件
    """
    input_file = "ni64x64.png"
    output_dir = "./output"
    output_file = os.path.join(output_dir, "ni64x64.c")
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        print("请确保 ni64x64.png 文件在当前目录下")
        return False
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    
    try:
        print(f"开始处理图片: {input_file}")
        
        # 1. 从PNG文件创建LVGL图像对象
        img = LVGLImage()
        img.from_png(
            filename=input_file,
            cf=ColorFormat.RGB565,  # 使用RGB565格式，适合嵌入式显示
            background=0x000000,    # 黑色背景
            rgb565_dither=False,    # 不使用抖动
            nema_gfx=False         # 不使用NEMA GFX格式
        )
        
        print(f"图片信息: {img.w}x{img.h} 像素, 格式: {img.cf.name}")
        print(f"原始数据大小: {img.data_len} 字节")
        
        # 2. 调整字节对齐 (可选)
        img.adjust_stride(align=1)  # 1字节对齐
        
        # 3. 生成压缩的C数组文件
        print("生成C文件...")
        img.to_c_array(
            filename=output_file,
            compress=CompressMethod.LZ4,  # 使用LZ4压缩
            outputname="ni64x64"         # C变量名
        )
        
        print(f"✓ 成功生成C文件: {output_file}")
        
        # 4. 显示生成的C文件信息
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"C文件大小: {file_size} 字节")
            
            # 显示C文件的开头部分
            print("\nC文件内容预览:")
            print("-" * 60)
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for i, line in enumerate(lines[:15], 1):  # 显示前15行
                    print(f"{i:2d}: {line.rstrip()}")
                if len(lines) > 15:
                    print("    ... (更多内容)")
            print("-" * 60)
            
            # 查找并显示图像描述符信息
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if "const lv_image_dsc_t ni64x64" in content:
                    print("\n✓ 图像描述符 'ni64x64' 已生成")
                    print("在您的C代码中可以这样使用:")
                    print("  extern const lv_image_dsc_t ni64x64;")
                    print("  lv_obj_t *img = lv_img_create(parent);")
                    print("  lv_img_set_src(img, &ni64x64);")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_test_image():
    """
    如果ni64x64.png不存在，创建一个测试图片
    """
    try:
        from PIL import Image, ImageDraw
        
        # 创建64x64的测试图片
        img = Image.new('RGB', (64, 64), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制一些简单的图形
        draw.rectangle([10, 10, 54, 54], fill='blue', outline='red', width=2)
        draw.ellipse([20, 20, 44, 44], fill='yellow')
        draw.text((25, 28), "NI", fill='black')
        
        img.save("ni64x64.png")
        print("✓ 创建了测试图片 ni64x64.png")
        return True
        
    except ImportError:
        print("需要安装PIL库来创建测试图片: pip install Pillow")
        return False
    except Exception as e:
        print(f"创建测试图片失败: {str(e)}")
        return False

def main():
    print("LVGL图片压缩工具 - ni64x64.png 专用版本")
    print("=" * 50)
    
    # 检查是否存在目标图片文件
    if not os.path.exists("ni64x64.png"):
        print("未找到 ni64x64.png 文件")
        
        # 询问是否创建测试图片
        try:
            choice = input("是否创建一个测试图片? (y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                if create_test_image():
                    print("测试图片创建成功，继续处理...")
                else:
                    print("无法创建测试图片，请手动放置 ni64x64.png 文件")
                    return
            else:
                print("请将 ni64x64.png 文件放在当前目录下，然后重新运行此脚本")
                return
        except KeyboardInterrupt:
            print("\n操作取消")
            return
    
    # 执行压缩处理
    success = compress_ni64x64_image()
    
    if success:
        print("\n🎉 处理完成!")
        print("生成的C文件可以直接在LVGL项目中使用")
    else:
        print("\n❌ 处理失败，请检查错误信息")

if __name__ == "__main__":
    main()
