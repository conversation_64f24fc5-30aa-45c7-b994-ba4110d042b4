import tkinter as tkfrom tkinter import ttk, messagebox

def calculate_crc(data):    """Calculate CRC based on the given algorithm"""    crc = 0
    for byte in data:
        crc ^= byte    return crc
def process_hex_input():    """Process hex input and calculate CRC"""
    try:
        # Get input and remove spaces        hex_str = hex_input.get().replace(" ", "")        
        # Convert hex string to bytes
        if len(hex_str) % 2 != 0:            messagebox.showerror("错误", "十六进制字符串长度必须为偶数")            return
            
        data = bytes.fromhex(hex_str)        
        # Calculate CRC        crc_result = calculate_crc(data)        
        # Display result        result_label.config(text=f"CRC结果: 0x{crc_result:02X}")
        
    except ValueError:        messagebox.showerror("错误", "无效的十六进制输入")
# Create main windowroot = tk.Tk()
root.title("CRC8计算器")root.geometry("400x200")

# Create and pack widgetsttk.Label(root, text="请输入十六进制数据 (可包含空格):", font=("SimHei", 11)).pack(pady=(20,5))
hex_input = ttk.Entry(root, width=40)hex_input.pack(pady=5)
ttk.Button(root, text="计算CRC", command=process_hex_input).pack(pady=10)

result_label = ttk.Label(root, text="CRC结果: ", font=("SimHei", 11))result_label.pack(pady=10)
# Start the application
root.mainloop()
























