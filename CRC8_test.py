import tkinter as tk
from tkinter import ttk, messagebox
import re

class CRC8Calculator:
    def __init__(self, root):
        self.root = root
        self.root.title("CRC8 计算器")
        self.root.geometry("500x400")

        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 输入标签
        ttk.Label(main_frame, text="输入十六进制数据 (数与数之间可以有空格):").grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 5))

        # 输入文本框
        self.input_text = tk.Text(main_frame, height=8, width=60)
        self.input_text.grid(row=1, column=0, columnspan=2, pady=(0, 10), sticky=(tk.W, tk.E))

        # 添加滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.input_text.yview)
        scrollbar.grid(row=1, column=2, sticky=(tk.N, tk.S))
        self.input_text.configure(yscrollcommand=scrollbar.set)

        # 计算按钮
        calc_button = ttk.Button(main_frame, text="计算 CRC8", command=self.calculate_crc)
        calc_button.grid(row=2, column=0, pady=(0, 10), sticky=tk.W)

        # 清空按钮
        clear_button = ttk.Button(main_frame, text="清空", command=self.clear_input)
        clear_button.grid(row=2, column=1, pady=(0, 10), sticky=tk.W, padx=(10, 0))

        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="计算结果", padding="10")
        result_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        # CRC结果标签
        ttk.Label(result_frame, text="CRC8 值:").grid(row=0, column=0, sticky=tk.W)
        self.crc_result = ttk.Label(result_frame, text="--", font=("Arial", 12, "bold"))
        self.crc_result.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        # 十六进制显示
        ttk.Label(result_frame, text="十六进制:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.hex_result = ttk.Label(result_frame, text="--", font=("Arial", 12, "bold"))
        self.hex_result.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))

        # 处理的数据显示
        ttk.Label(result_frame, text="处理的数据:").grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        self.processed_data = ttk.Label(result_frame, text="--", wraplength=400)
        self.processed_data.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))

        # 配置网格权重
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

    def calculate_crc8(self, data_bytes):
        """
        按照提供的算法计算CRC8
        uint8_t crc = 0;
        for (uint8_t i = 0; i < len; i++)
        {
            crc ^= data[i];
        }
        return crc;
        """
        crc = 0
        for byte in data_bytes:
            crc ^= byte
        return crc & 0xFF  # 确保结果是8位

    def parse_hex_input(self, input_str):
        """解析十六进制输入，忽略空格"""
        # 移除所有空格和换行符
        cleaned = re.sub(r'\s+', '', input_str)

        # 检查是否只包含有效的十六进制字符
        if not re.match(r'^[0-9A-Fa-f]*$', cleaned):
            raise ValueError("输入包含无效的十六进制字符")

        # 如果长度为奇数，在前面补0
        if len(cleaned) % 2 == 1:
            cleaned = '0' + cleaned

        # 转换为字节数组
        data_bytes = []
        for i in range(0, len(cleaned), 2):
            hex_byte = cleaned[i:i+2]
            data_bytes.append(int(hex_byte, 16))

        return data_bytes, cleaned

    def calculate_crc(self):
        """计算CRC并显示结果"""
        try:
            input_str = self.input_text.get("1.0", tk.END).strip()

            if not input_str:
                messagebox.showwarning("警告", "请输入十六进制数据")
                return

            # 解析输入
            data_bytes, _ = self.parse_hex_input(input_str)

            if not data_bytes:
                messagebox.showwarning("警告", "没有有效的十六进制数据")
                return

            # 计算CRC
            crc_value = self.calculate_crc8(data_bytes)

            # 显示结果
            self.crc_result.config(text=str(crc_value))
            self.hex_result.config(text=f"0x{crc_value:02X}")

            # 格式化显示处理的数据
            formatted_data = ' '.join([f"{b:02X}" for b in data_bytes])
            self.processed_data.config(text=formatted_data)

        except ValueError as e:
            messagebox.showerror("错误", str(e))
        except Exception as e:
            messagebox.showerror("错误", f"计算过程中发生错误: {str(e)}")

    def clear_input(self):
        """清空输入和结果"""
        self.input_text.delete("1.0", tk.END)
        self.crc_result.config(text="--")
        self.hex_result.config(text="--")
        self.processed_data.config(text="--")

def main():
    root = tk.Tk()
    CRC8Calculator(root)
    root.mainloop()

if __name__ == "__main__":
    main()