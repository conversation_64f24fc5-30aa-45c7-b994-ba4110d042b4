# 优化的随机数生成器，基于游戏时间的随机值生成和统计

import matplotlib.pyplot as plt
import numpy as np
import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib
from matplotlib import font_manager
matplotlib.use('TkAgg')  # 修复中文显示问题600

# 设置matplotlib支持中文
font_manager.fontManager.addfont('C:/Windows/Fonts/simhei.ttf')
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def get_user_input():
    """通过Tkinter界面获取用户输入的游戏时间参数"""
    def on_submit():
        try:
            gt = int(entry_game_time.get())
            gtl = int(entry_game_time_last.get())
            if gt < -32768 or gt > 32767:
                messagebox.showwarning("警告", "GameTime 超出范围，使用默认值 100")
                gt_val.set("100")
                gt = 100
            if gtl < -32768 or gtl > 32767:
                messagebox.showwarning("警告", "GameTime_Last 超出范围，使用默认值 -50")
                gtl_val.set("-50")
                gtl = -50
            root.user_input = (gt, gtl)
            root.destroy()
        except Exception:
            messagebox.showerror("错误", "输入无效，使用默认值")
            root.user_input = (100, -50)
            root.destroy()

    root = tk.Tk()
    root.title("游戏时间参数输入")
    root.geometry("320x160")
    gt_val = tk.StringVar(value="100")
    gtl_val = tk.StringVar(value="-50")

    ttk.Label(root, text="请输入 GameTime (int16_t, -32768 到 32767):", font=("SimHei", 11)).pack(pady=(15, 2))
    entry_game_time = ttk.Entry(root, textvariable=gt_val, font=("SimHei", 11))
    entry_game_time.pack()

    ttk.Label(root, text="请输入 GameTime_Last (int16_t, -32768 到 32767):", font=("SimHei", 11)).pack(pady=(10, 2))
    entry_game_time_last = ttk.Entry(root, textvariable=gtl_val, font=("SimHei", 11))
    entry_game_time_last.pack()

    submit_btn = ttk.Button(root, text="确定", command=on_submit)
    submit_btn.pack(pady=12)

    root.user_input = (100, -50)
    root.mainloop()
    return root.user_input
def disturb(ms):
    """原始的扰动函数"""
    random = ms & 0xFFFF
    random ^= (random << 3) & 0xFFFF
    random ^= (random >> 5) & 0xFFFF
    random ^= (random << 2) & 0xFFFF
    return random & 0xFFFF

class GameTimeRandomizer:
    def __init__(self):
        self.increase_count = 0
        self.decrease_count = 0
        self.total_count = 0
        self.history = []

    def generate_random_with_gametime(self, ms, game_time, game_time_last):
        """
        基于游戏时间生成随机数

        Args:
            ms: 毫秒时间戳
            game_time: int16_t 当前游戏时间
            game_time_last: int16_t 上次游戏时间

        Returns:
            tuple: (最终随机值, 是否增加, 增加概率, 减少概率)
        """
        # 生成基础随机数
        random = disturb(ms)

        # 计算游戏时间相关参数
        abs_game_time_last = abs(game_time_last) & 0xFFFF
        game_time_acc = (game_time + abs_game_time_last) & 0xFFFF

        # 映射随机数到游戏时间累积范围
        if game_time_acc == 0:
            map_game_time_acc = 0
        else:
            map_game_time_acc = int((random / 0xFFFF) * game_time_acc)

        # 判断增加还是减少
        is_increase = False
        final_value = 0

        if map_game_time_acc >= abs_game_time_last:
            # 增加值
            final_value = map_game_time_acc - abs_game_time_last
            is_increase = True
            self.increase_count += 1
        else:
            # 减少值
            final_value = abs_game_time_last - map_game_time_acc
            is_increase = False
            self.decrease_count += 1

        self.total_count += 1

        # 记录历史
        self.history.append({
            'ms': ms,
            'game_time': game_time,
            'game_time_last': game_time_last,
            'random': random,
            'final_value': final_value,
            'is_increase': is_increase
        })

        # 计算概率
        increase_prob = self.increase_count / self.total_count if self.total_count > 0 else 0
        decrease_prob = self.decrease_count / self.total_count if self.total_count > 0 else 0

        return final_value, is_increase, increase_prob, decrease_prob

    def get_statistics(self):
        """获取统计信息"""
        if self.total_count == 0:
            return {
                'total_count': 0,
                'increase_count': 0,
                'decrease_count': 0,
                'increase_probability': 0,
                'decrease_probability': 0
            }

        return {
            'total_count': self.total_count,
            'increase_count': self.increase_count,
            'decrease_count': self.decrease_count,
            'increase_probability': self.increase_count / self.total_count,
            'decrease_probability': self.decrease_count / self.total_count
        }

    def reset_statistics(self):
        """重置统计信息"""
        self.increase_count = 0
        self.decrease_count = 0
        self.total_count = 0
        self.history = []

# 测试和可视化
def test_game_time_randomizer():
    randomizer = GameTimeRandomizer()

    # 模拟测试数据
    test_samples = 1000
    ms_values = np.random.randint(0, 65536, test_samples)
    game_times = np.random.randint(-32768, 32767, test_samples)
    game_times_last = np.random.randint(-32768, 32767, test_samples)

    results = []
    increase_probs = []
    decrease_probs = []

    print("开始测试游戏时间随机数生成器...")
    print(f"{'序号':<6} {'ms':<8} {'GameTime':<10} {'LastTime':<10} {'随机值':<8} {'结果':<8} {'操作':<6} {'增加概率':<10} {'减少概率':<10}")
    print("-" * 80)

    for i in range(min(20, test_samples)):  # 只显示前20个结果
        ms = ms_values[i]
        gt = game_times[i]
        gtl = game_times_last[i]

        final_val, is_inc, inc_prob, dec_prob = randomizer.generate_random_with_gametime(ms, gt, gtl)

        results.append(final_val)
        increase_probs.append(inc_prob)
        decrease_probs.append(dec_prob)

        operation = "增加" if is_inc else "减少"
        print(f"{i+1:<6} {ms:<8} {gt:<10} {gtl:<10} {disturb(ms):<8} {final_val:<8} {operation:<6} {inc_prob:<10.3f} {dec_prob:<10.3f}")

    # 处理剩余的样本（不显示）
    for i in range(20, test_samples):
        ms = ms_values[i]
        gt = game_times[i]
        gtl = game_times_last[i]

        final_val, is_inc, inc_prob, dec_prob = randomizer.generate_random_with_gametime(ms, gt, gtl)
        results.append(final_val)
        increase_probs.append(inc_prob)
        decrease_probs.append(dec_prob)

    # 显示最终统计
    stats = randomizer.get_statistics()
    print(f"\n最终统计结果:")
    print(f"总次数: {stats['total_count']}")
    print(f"增加次数: {stats['increase_count']}")
    print(f"减少次数: {stats['decrease_count']}")
    print(f"增加概率: {stats['increase_probability']:.3f}")
    print(f"减少概率: {stats['decrease_probability']:.3f}")

    return results, increase_probs, decrease_probs, randomizer

# 固定游戏时间参数下的随机数变化分析
def analyze_fixed_gametime_random_variation(game_time, game_time_last):
    """
    分析在固定的game_time和game_time_last下，random从0到最大值的变化

    Args:
        game_time: 固定的游戏时间
        game_time_last: 固定的上次游戏时间
    """
    print(f"\n=== 固定游戏时间参数分析 ===")
    print(f"GameTime: {game_time}")
    print(f"GameTime_Last: {game_time_last}")

    # 计算关键参数
    abs_game_time_last = abs(game_time_last) & 0xFFFF
    game_time_acc = (game_time + abs_game_time_last) & 0xFFFF

    print(f"abs_GameTime_Last: {abs_game_time_last}")
    print(f"GameTimeACC: {game_time_acc}")
    print(f"最大 map_GameTimeACC: {game_time_acc}")

    # 生成random值序列（从0到0xFFFF，取样本）
    random_samples = np.linspace(0, 0xFFFF, 1000, dtype=int)

    results = []
    operations = []
    map_values = []

    increase_count = 0
    decrease_count = 0

    print(f"\n{'Random':<8} {'MapValue':<10} {'Result':<8} {'Operation':<10}")
    print("-" * 40)

    # 显示前20个样本的详细信息
    for i, random_val in enumerate(random_samples[:20]):
        # 映射随机数到游戏时间累积范围
        if game_time_acc == 0:
            map_game_time_acc = 0
        else:
            map_game_time_acc = int((random_val / 0xFFFF) * game_time_acc)

        # 判断增加还是减少
        if map_game_time_acc >= abs_game_time_last:
            # 增加值
            final_value = map_game_time_acc - abs_game_time_last
            operation = "增加"
            increase_count += 1
        else:
            # 减少值
            final_value = abs_game_time_last - map_game_time_acc
            operation = "减少"
            decrease_count += 1

        results.append(final_value)
        operations.append(operation)
        map_values.append(map_game_time_acc)

        print(f"{random_val:<8} {map_game_time_acc:<10} {final_value:<8} {operation:<10}")

    # 处理剩余样本（不显示详细信息）
    for random_val in random_samples[20:]:
        if game_time_acc == 0:
            map_game_time_acc = 0
        else:
            map_game_time_acc = int((random_val / 0xFFFF) * game_time_acc)

        if map_game_time_acc >= abs_game_time_last:
            final_value = map_game_time_acc - abs_game_time_last
            operation = "增加"
            increase_count += 1
        else:
            final_value = abs_game_time_last - map_game_time_acc
            operation = "减少"
            decrease_count += 1

        results.append(final_value)
        operations.append(operation)
        map_values.append(map_game_time_acc)

    total_count = len(random_samples)
    increase_prob = increase_count / total_count
    decrease_prob = decrease_count / total_count

    print(f"\n统计结果:")
    print(f"总样本数: {total_count}")
    print(f"增加次数: {increase_count}")
    print(f"减少次数: {decrease_count}")
    print(f"增加概率: {increase_prob:.3f}")
    print(f"减少概率: {decrease_prob:.3f}")

    # 找到临界点
    critical_random = int((abs_game_time_last / game_time_acc) * 0xFFFF) if game_time_acc > 0 else 0
    print(f"临界Random值 (增加/减少分界点): {critical_random}")

    return random_samples, map_values, results, operations, {
        'abs_game_time_last': abs_game_time_last,
        'game_time_acc': game_time_acc,
        'increase_count': increase_count,
        'decrease_count': decrease_count,
        'increase_prob': increase_prob,
        'decrease_prob': decrease_prob,
        'critical_random': critical_random
    }

# 可视化固定游戏时间下的变化
def visualize_fixed_gametime_analysis(random_samples, map_values, results, operations, stats):
    """可视化固定游戏时间参数下的分析结果"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 1. Random vs MapValue 关系图
    ax1.plot(random_samples, map_values, 'b-', linewidth=2, label='MapValue')
    ax1.axhline(y=stats['abs_game_time_last'], color='r', linestyle='--',
                label=f'abs_GameTime_Last = {stats["abs_game_time_last"]}')
    ax1.axvline(x=stats['critical_random'], color='g', linestyle='--',
                label=f'Critical Random = {stats["critical_random"]}')
    ax1.set_xlabel('Random Value (0-65535)')
    ax1.set_ylabel('Map GameTime ACC')
    ax1.set_title('Random vs MapValue Relationship')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. Random vs Final Result
    increase_mask = np.array([op == "增加" for op in operations])
    decrease_mask = ~increase_mask

    ax2.scatter(np.array(random_samples)[increase_mask], np.array(results)[increase_mask],
               c='green', alpha=0.6, s=10, label='增加操作')
    ax2.scatter(np.array(random_samples)[decrease_mask], np.array(results)[decrease_mask],
               c='red', alpha=0.6, s=10, label='减少操作')
    ax2.axvline(x=stats['critical_random'], color='black', linestyle='--',
                label=f'临界点 = {stats["critical_random"]}')
    ax2.set_xlabel('Random Value (0-65535)')
    ax2.set_ylabel('Final Result')
    ax2.set_title('Random vs Final Result (Color by Operation)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. 结果值分布直方图
    ax3.hist(results, bins=50, alpha=0.7, color='blue', edgecolor='black')
    ax3.set_xlabel('Final Result Value')
    ax3.set_ylabel('Frequency')
    ax3.set_title('Distribution of Final Results')
    ax3.grid(True, alpha=0.3)

    # 4. 增加/减少操作比例
    labels = ['增加', '减少']
    sizes = [stats['increase_count'], stats['decrease_count']]
    colors = ['green', 'red']

    wedges, texts, autotexts = ax4.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                                       startangle=90)
    ax4.set_title('Operation Distribution')

    # 添加统计信息文本
    info_text = f"""参数信息:
GameTime: {stats['game_time_acc'] - stats['abs_game_time_last']}
GameTime_Last: {-stats['abs_game_time_last'] if stats['abs_game_time_last'] > 0 else 0}
abs_GameTime_Last: {stats['abs_game_time_last']}
GameTimeACC: {stats['game_time_acc']}
临界Random: {stats['critical_random']}
增加概率: {stats['increase_prob']:.3f}
减少概率: {stats['decrease_prob']:.3f}"""

    plt.figtext(0.02, 0.02, info_text, fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))

    plt.tight_layout()
    plt.show()

# UI输入函数
def get_user_input():
    """获取用户输入的游戏时间参数"""
    print("=== 游戏时间参数输入 ===")
    try:
        game_time = int(input("请输入 GameTime (int16_t, -32768 到 32767): "))
        if game_time < -32768 or game_time > 32767:
            print("GameTime 超出范围，使用默认值 100")
            game_time = 100

        game_time_last = int(input("请输入 GameTime_Last (int16_t, -32768 到 32767): "))
        if game_time_last < -32768 or game_time_last > 32767:
            print("GameTime_Last 超出范围，使用默认值 -50")
            game_time_last = -50

        return game_time, game_time_last
    except ValueError:
        print("输入无效，使用默认值")
        return 100, -50

# 运行测试
if __name__ == "__main__":
    print("=== 优化随机数生成器 - 固定参数分析模式 ===")

    # 获取用户输入
    game_time, game_time_last = get_user_input()

    # 分析固定参数下的变化
    random_samples, map_values, results, operations, stats = analyze_fixed_gametime_random_variation(
        game_time, game_time_last)

    # 可视化结果
    print("\n生成可视化图表...")
    visualize_fixed_gametime_analysis(random_samples, map_values, results, operations, stats)





